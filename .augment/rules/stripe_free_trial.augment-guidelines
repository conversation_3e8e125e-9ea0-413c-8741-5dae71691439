# Configure free trials

Delay payments on subscriptions using free trial periods.

Stripe doesn’t set a specific limit on a free trial length. Most businesses use shorter trials (such as 30 days), but you can set longer periods. However, consider:

- Keeping payment methods valid for post-trial charges
- Potential impact on conversion rates with longer trials

# Stripe-hosted page

> This is a Stripe-hosted page for when payment-ui is stripe-hosted. View the full page at https://docs.stripe.com/payments/checkout/free-trials?payment-ui=stripe-hosted.

You can configure a Checkout Session to start a customer’s subscription with a free trial by passing one of the following parameters:

- [subscription_data.trial_period_days](https://docs.stripe.com/api/checkout/sessions/create#create_checkout_session-subscription_data-trial_period_days.md), the length (in days) of your free trial.
- [subscription_data.trial_end](https://docs.stripe.com/api/checkout/sessions/create#create_checkout_session-subscription_data-trial_end.md), a Unix timestamp representing the end of the trial period.

```curl
curl https://api.stripe.com/v1/checkout/sessions \
  -u "<<YOUR_SECRET_KEY>>:" \
  -d mode=subscription \
  --data-urlencode success_url="https://example.com/success" \
  --data-urlencode cancel_url="https://example.com/cancel" \
  -d "line_items[0][price]"="{{PRICE_ID}}" \
  -d "line_items[0][quantity]"=1 \
  -d "subscription_data[trial_period_days]"=30
```

```cli
stripe checkout sessions create  \
  --mode=subscription \
  --success-url="https://example.com/success" \
  --cancel-url="https://example.com/cancel" \
  -d "line_items[0][price]"="{{PRICE_ID}}" \
  -d "line_items[0][quantity]"=1 \
  -d "subscription_data[trial_period_days]"=30
```

```ruby
Stripe.api_key = '<<YOUR_SECRET_KEY>>'

session = Stripe::Checkout::Session.create({
  mode: 'subscription',
  success_url: 'https://example.com/success',
  cancel_url: 'https://example.com/cancel',
  line_items: [
    {
      price: '{{PRICE_ID}}',
      quantity: 1,
    },
  ],
  subscription_data: {trial_period_days: 30},
})
```

```ruby
client = Stripe::StripeClient.new("<<YOUR_SECRET_KEY>>")

session = client.v1.checkout.sessions.create({
  mode: 'subscription',
  success_url: 'https://example.com/success',
  cancel_url: 'https://example.com/cancel',
  line_items: [
    {
      price: '{{PRICE_ID}}',
      quantity: 1,
    },
  ],
  subscription_data: {trial_period_days: 30},
})
```

```python
import stripe
stripe.api_key = "<<YOUR_SECRET_KEY>>"

session = stripe.checkout.Session.create(
  mode="subscription",
  success_url="https://example.com/success",
  cancel_url="https://example.com/cancel",
  line_items=[{"price": "{{PRICE_ID}}", "quantity": 1}],
  subscription_data={"trial_period_days": 30},
)
```

```python
client = StripeClient("<<YOUR_SECRET_KEY>>")

session = client.checkout.sessions.create({
  "mode": "subscription",
  "success_url": "https://example.com/success",
  "cancel_url": "https://example.com/cancel",
  "line_items": [{"price": "{{PRICE_ID}}", "quantity": 1}],
  "subscription_data": {"trial_period_days": 30},
})
```

```php
$stripe = new \Stripe\StripeClient('<<YOUR_SECRET_KEY>>');

$session = $stripe->checkout->sessions->create([
  'mode' => 'subscription',
  'success_url' => 'https://example.com/success',
  'cancel_url' => 'https://example.com/cancel',
  'line_items' => [
    [
      'price' => '{{PRICE_ID}}',
      'quantity' => 1,
    ],
  ],
  'subscription_data' => ['trial_period_days' => 30],
]);
```

```java
Stripe.apiKey = "<<YOUR_SECRET_KEY>>";

SessionCreateParams params =
  SessionCreateParams.builder()
    .setMode(SessionCreateParams.Mode.SUBSCRIPTION)
    .setSuccessUrl("https://example.com/success")
    .setCancelUrl("https://example.com/cancel")
    .addLineItem(
      SessionCreateParams.LineItem.builder()
        .setPrice("{{PRICE_ID}}")
        .setQuantity(1L)
        .build()
    )
    .setSubscriptionData(
      SessionCreateParams.SubscriptionData.builder().setTrialPeriodDays(30L).build()
    )
    .build();

Session session = Session.create(params);
```

```java
StripeClient client = new StripeClient("<<YOUR_SECRET_KEY>>");

SessionCreateParams params =
  SessionCreateParams.builder()
    .setMode(SessionCreateParams.Mode.SUBSCRIPTION)
    .setSuccessUrl("https://example.com/success")
    .setCancelUrl("https://example.com/cancel")
    .addLineItem(
      SessionCreateParams.LineItem.builder()
        .setPrice("{{PRICE_ID}}")
        .setQuantity(1L)
        .build()
    )
    .setSubscriptionData(
      SessionCreateParams.SubscriptionData.builder().setTrialPeriodDays(30L).build()
    )
    .build();

Session session = client.checkout().sessions().create(params);
```

```node
const stripe = require('stripe')('<<YOUR_SECRET_KEY>>');

const session = await stripe.checkout.sessions.create({
  mode: 'subscription',
  success_url: 'https://example.com/success',
  cancel_url: 'https://example.com/cancel',
  line_items: [
    {
      price: '{{PRICE_ID}}',
      quantity: 1,
    },
  ],
  subscription_data: {
    trial_period_days: 30,
  },
});
```

```go
stripe.Key = "<<YOUR_SECRET_KEY>>"

params := &stripe.CheckoutSessionParams{
  Mode: stripe.String(stripe.CheckoutSessionModeSubscription),
  SuccessURL: stripe.String("https://example.com/success"),
  CancelURL: stripe.String("https://example.com/cancel"),
  LineItems: []*stripe.CheckoutSessionLineItemParams{
    &stripe.CheckoutSessionLineItemParams{
      Price: stripe.String("{{PRICE_ID}}"),
      Quantity: stripe.Int64(1),
    },
  },
  SubscriptionData: &stripe.CheckoutSessionSubscriptionDataParams{
    TrialPeriodDays: stripe.Int64(30),
  },
}
result, err := session.New(params)
```

```go
sc := stripe.NewClient("<<YOUR_SECRET_KEY>>")
params := &stripe.CheckoutSessionCreateParams{
  Mode: stripe.String(stripe.CheckoutSessionModeSubscription),
  SuccessURL: stripe.String("https://example.com/success"),
  CancelURL: stripe.String("https://example.com/cancel"),
  LineItems: []*stripe.CheckoutSessionCreateLineItemParams{
    &stripe.CheckoutSessionCreateLineItemParams{
      Price: stripe.String("{{PRICE_ID}}"),
      Quantity: stripe.Int64(1),
    },
  },
  SubscriptionData: &stripe.CheckoutSessionCreateSubscriptionDataParams{
    TrialPeriodDays: stripe.Int64(30),
  },
}
result, err := sc.V1CheckoutSessions.Create(context.TODO(), params)
```

```dotnet
StripeConfiguration.ApiKey = "<<YOUR_SECRET_KEY>>";

var options = new Stripe.Checkout.SessionCreateOptions
{
    Mode = "subscription",
    SuccessUrl = "https://example.com/success",
    CancelUrl = "https://example.com/cancel",
    LineItems = new List<Stripe.Checkout.SessionLineItemOptions>
    {
        new Stripe.Checkout.SessionLineItemOptions
        {
            Price = "{{PRICE_ID}}",
            Quantity = 1,
        },
    },
    SubscriptionData = new Stripe.Checkout.SessionSubscriptionDataOptions
    {
        TrialPeriodDays = 30,
    },
};
var service = new Stripe.Checkout.SessionService();
Stripe.Checkout.Session session = service.Create(options);
```

```dotnet
var options = new Stripe.Checkout.SessionCreateOptions
{
    Mode = "subscription",
    SuccessUrl = "https://example.com/success",
    CancelUrl = "https://example.com/cancel",
    LineItems = new List<Stripe.Checkout.SessionLineItemOptions>
    {
        new Stripe.Checkout.SessionLineItemOptions
        {
            Price = "{{PRICE_ID}}",
            Quantity = 1,
        },
    },
    SubscriptionData = new Stripe.Checkout.SessionSubscriptionDataOptions
    {
        TrialPeriodDays = 30,
    },
};
var client = new StripeClient("<<YOUR_SECRET_KEY>>");
var service = client.V1.Checkout.Sessions;
Stripe.Checkout.Session session = service.Create(options);
```

## Free trials without collecting a payment method 

By default, Checkout Sessions collect a payment method to use after the trial ends. You can sign customers up for free trials without collecting payment details by passing [payment_method_collection=if_required](https://docs.stripe.com/api/checkout/sessions/create#create_checkout_session-payment_method_collection.md).

Choose whether to cancel or pause the subscription if the customer doesn’t provide a payment method before the trial ends by passing [trial_settings.end_behavior.missing_payment_method](https://docs.stripe.com/api/checkout/sessions/create#create_checkout_session-subscription_data-trial_settings-end_behavior-missing_payment_method.md).

- **Cancel subscription**-If the free trial subscription ends without a payment method, it cancels immediately. You can create another subscription if the customer decides to subscribe to a paid plan in the future.
- **Pause subscription**-If the free trial subscription ends without a payment method, it pauses and doesn’t cycle until it’s resumed. When a subscription is paused, it doesn’t generate invoices (unlike when a subscription’s [payment collection](https://docs.stripe.com/billing/subscriptions/pause-payment.md) is paused). When your customer adds their payment method after the subscription has paused, you can resume the same subscription. The subscription can remain paused indefinitely.

```curl
curl https://api.stripe.com/v1/checkout/sessions \
  -u "<<YOUR_SECRET_KEY>>:" \
  -d mode=subscription \
  --data-urlencode success_url="https://example.com/success" \
  --data-urlencode cancel_url="https://example.com/cancel" \
  -d "line_items[0][price]"="{{PRICE_ID}}" \
  -d "line_items[0][quantity]"=1 \
  -d "subscription_data[trial_period_days]"=30 \
  -d "subscription_data[trial_settings][end_behavior][missing_payment_method]"=cancel \
  -d payment_method_collection=if_required
```

```cli
stripe checkout sessions create  \
  --mode=subscription \
  --success-url="https://example.com/success" \
  --cancel-url="https://example.com/cancel" \
  -d "line_items[0][price]"="{{PRICE_ID}}" \
  -d "line_items[0][quantity]"=1 \
  -d "subscription_data[trial_period_days]"=30 \
  -d "subscription_data[trial_settings][end_behavior][missing_payment_method]"=cancel \
  --payment-method-collection=if_required
```

```ruby
Stripe.api_key = '<<YOUR_SECRET_KEY>>'

session = Stripe::Checkout::Session.create({
  mode: 'subscription',
  success_url: 'https://example.com/success',
  cancel_url: 'https://example.com/cancel',
  line_items: [
    {
      price: '{{PRICE_ID}}',
      quantity: 1,
    },
  ],
  subscription_data: {
    trial_period_days: 30,
    trial_settings: {end_behavior: {missing_payment_method: 'cancel'}},
  },
  payment_method_collection: 'if_required',
})
```

```ruby
client = Stripe::StripeClient.new("<<YOUR_SECRET_KEY>>")

session = client.v1.checkout.sessions.create({
  mode: 'subscription',
  success_url: 'https://example.com/success',
  cancel_url: 'https://example.com/cancel',
  line_items: [
    {
      price: '{{PRICE_ID}}',
      quantity: 1,
    },
  ],
  subscription_data: {
    trial_period_days: 30,
    trial_settings: {end_behavior: {missing_payment_method: 'cancel'}},
  },
  payment_method_collection: 'if_required',
})
```

```python
import stripe
stripe.api_key = "<<YOUR_SECRET_KEY>>"

session = stripe.checkout.Session.create(
  mode="subscription",
  success_url="https://example.com/success",
  cancel_url="https://example.com/cancel",
  line_items=[{"price": "{{PRICE_ID}}", "quantity": 1}],
  subscription_data={
    "trial_period_days": 30,
    "trial_settings": {"end_behavior": {"missing_payment_method": "cancel"}},
  },
  payment_method_collection="if_required",
)
```

```python
client = StripeClient("<<YOUR_SECRET_KEY>>")

session = client.checkout.sessions.create({
  "mode": "subscription",
  "success_url": "https://example.com/success",
  "cancel_url": "https://example.com/cancel",
  "line_items": [{"price": "{{PRICE_ID}}", "quantity": 1}],
  "subscription_data": {
    "trial_period_days": 30,
    "trial_settings": {"end_behavior": {"missing_payment_method": "cancel"}},
  },
  "payment_method_collection": "if_required",
})
```

```php
$stripe = new \Stripe\StripeClient('<<YOUR_SECRET_KEY>>');

$session = $stripe->checkout->sessions->create([
  'mode' => 'subscription',
  'success_url' => 'https://example.com/success',
  'cancel_url' => 'https://example.com/cancel',
  'line_items' => [
    [
      'price' => '{{PRICE_ID}}',
      'quantity' => 1,
    ],
  ],
  'subscription_data' => [
    'trial_period_days' => 30,
    'trial_settings' => ['end_behavior' => ['missing_payment_method' => 'cancel']],
  ],
  'payment_method_collection' => 'if_required',
]);
```

```java
Stripe.apiKey = "<<YOUR_SECRET_KEY>>";

SessionCreateParams params =
  SessionCreateParams.builder()
    .setMode(SessionCreateParams.Mode.SUBSCRIPTION)
    .setSuccessUrl("https://example.com/success")
    .setCancelUrl("https://example.com/cancel")
    .addLineItem(
      SessionCreateParams.LineItem.builder()
        .setPrice("{{PRICE_ID}}")
        .setQuantity(1L)
        .build()
    )
    .setSubscriptionData(
      SessionCreateParams.SubscriptionData.builder()
        .setTrialPeriodDays(30L)
        .setTrialSettings(
          SessionCreateParams.SubscriptionData.TrialSettings.builder()
            .setEndBehavior(
              SessionCreateParams.SubscriptionData.TrialSettings.EndBehavior.builder()
                .setMissingPaymentMethod(
                  SessionCreateParams.SubscriptionData.TrialSettings.EndBehavior.MissingPaymentMethod.CANCEL
                )
                .build()
            )
            .build()
        )
        .build()
    )
    .setPaymentMethodCollection(SessionCreateParams.PaymentMethodCollection.IF_REQUIRED)
    .build();

Session session = Session.create(params);
```

```java
StripeClient client = new StripeClient("<<YOUR_SECRET_KEY>>");

SessionCreateParams params =
  SessionCreateParams.builder()
    .setMode(SessionCreateParams.Mode.SUBSCRIPTION)
    .setSuccessUrl("https://example.com/success")
    .setCancelUrl("https://example.com/cancel")
    .addLineItem(
      SessionCreateParams.LineItem.builder()
        .setPrice("{{PRICE_ID}}")
        .setQuantity(1L)
        .build()
    )
    .setSubscriptionData(
      SessionCreateParams.SubscriptionData.builder()
        .setTrialPeriodDays(30L)
        .setTrialSettings(
          SessionCreateParams.SubscriptionData.TrialSettings.builder()
            .setEndBehavior(
              SessionCreateParams.SubscriptionData.TrialSettings.EndBehavior.builder()
                .setMissingPaymentMethod(
                  SessionCreateParams.SubscriptionData.TrialSettings.EndBehavior.MissingPaymentMethod.CANCEL
                )
                .build()
            )
            .build()
        )
        .build()
    )
    .setPaymentMethodCollection(SessionCreateParams.PaymentMethodCollection.IF_REQUIRED)
    .build();

Session session = client.checkout().sessions().create(params);
```

```node
const stripe = require('stripe')('<<YOUR_SECRET_KEY>>');

const session = await stripe.checkout.sessions.create({
  mode: 'subscription',
  success_url: 'https://example.com/success',
  cancel_url: 'https://example.com/cancel',
  line_items: [
    {
      price: '{{PRICE_ID}}',
      quantity: 1,
    },
  ],
  subscription_data: {
    trial_period_days: 30,
    trial_settings: {
      end_behavior: {
        missing_payment_method: 'cancel',
      },
    },
  },
  payment_method_collection: 'if_required',
});
```

```go
stripe.Key = "<<YOUR_SECRET_KEY>>"

params := &stripe.CheckoutSessionParams{
  Mode: stripe.String(stripe.CheckoutSessionModeSubscription),
  SuccessURL: stripe.String("https://example.com/success"),
  CancelURL: stripe.String("https://example.com/cancel"),
  LineItems: []*stripe.CheckoutSessionLineItemParams{
    &stripe.CheckoutSessionLineItemParams{
      Price: stripe.String("{{PRICE_ID}}"),
      Quantity: stripe.Int64(1),
    },
  },
  SubscriptionData: &stripe.CheckoutSessionSubscriptionDataParams{
    TrialPeriodDays: stripe.Int64(30),
    TrialSettings: &stripe.CheckoutSessionSubscriptionDataTrialSettingsParams{
      EndBehavior: &stripe.CheckoutSessionSubscriptionDataTrialSettingsEndBehaviorParams{
        MissingPaymentMethod: stripe.String("cancel"),
      },
    },
  },
  PaymentMethodCollection: stripe.String(stripe.CheckoutSessionPaymentMethodCollectionIfRequired),
}
result, err := session.New(params)
```

```go
sc := stripe.NewClient("<<YOUR_SECRET_KEY>>")
params := &stripe.CheckoutSessionCreateParams{
  Mode: stripe.String(stripe.CheckoutSessionModeSubscription),
  SuccessURL: stripe.String("https://example.com/success"),
  CancelURL: stripe.String("https://example.com/cancel"),
  LineItems: []*stripe.CheckoutSessionCreateLineItemParams{
    &stripe.CheckoutSessionCreateLineItemParams{
      Price: stripe.String("{{PRICE_ID}}"),
      Quantity: stripe.Int64(1),
    },
  },
  SubscriptionData: &stripe.CheckoutSessionCreateSubscriptionDataParams{
    TrialPeriodDays: stripe.Int64(30),
    TrialSettings: &stripe.CheckoutSessionCreateSubscriptionDataTrialSettingsParams{
      EndBehavior: &stripe.CheckoutSessionCreateSubscriptionDataTrialSettingsEndBehaviorParams{
        MissingPaymentMethod: stripe.String("cancel"),
      },
    },
  },
  PaymentMethodCollection: stripe.String(stripe.CheckoutSessionPaymentMethodCollectionIfRequired),
}
result, err := sc.V1CheckoutSessions.Create(context.TODO(), params)
```

```dotnet
StripeConfiguration.ApiKey = "<<YOUR_SECRET_KEY>>";

var options = new Stripe.Checkout.SessionCreateOptions
{
    Mode = "subscription",
    SuccessUrl = "https://example.com/success",
    CancelUrl = "https://example.com/cancel",
    LineItems = new List<Stripe.Checkout.SessionLineItemOptions>
    {
        new Stripe.Checkout.SessionLineItemOptions
        {
            Price = "{{PRICE_ID}}",
            Quantity = 1,
        },
    },
    SubscriptionData = new Stripe.Checkout.SessionSubscriptionDataOptions
    {
        TrialPeriodDays = 30,
        TrialSettings = new Stripe.Checkout.SessionSubscriptionDataTrialSettingsOptions
        {
            EndBehavior = new Stripe.Checkout.SessionSubscriptionDataTrialSettingsEndBehaviorOptions
            {
                MissingPaymentMethod = "cancel",
            },
        },
    },
    PaymentMethodCollection = "if_required",
};
var service = new Stripe.Checkout.SessionService();
Stripe.Checkout.Session session = service.Create(options);
```

```dotnet
var options = new Stripe.Checkout.SessionCreateOptions
{
    Mode = "subscription",
    SuccessUrl = "https://example.com/success",
    CancelUrl = "https://example.com/cancel",
    LineItems = new List<Stripe.Checkout.SessionLineItemOptions>
    {
        new Stripe.Checkout.SessionLineItemOptions
        {
            Price = "{{PRICE_ID}}",
            Quantity = 1,
        },
    },
    SubscriptionData = new Stripe.Checkout.SessionSubscriptionDataOptions
    {
        TrialPeriodDays = 30,
        TrialSettings = new Stripe.Checkout.SessionSubscriptionDataTrialSettingsOptions
        {
            EndBehavior = new Stripe.Checkout.SessionSubscriptionDataTrialSettingsEndBehaviorOptions
            {
                MissingPaymentMethod = "cancel",
            },
        },
    },
    PaymentMethodCollection = "if_required",
};
var client = new StripeClient("<<YOUR_SECRET_KEY>>");
var service = client.V1.Checkout.Sessions;
Stripe.Checkout.Session session = service.Create(options);
```

### Collect payment details automatically 

Before the trial expires, collect payment details from your customer.

Under **Manage free trial messaging** in your [Subscriptions and emails settings](https://dashboard.stripe.com/settings/billing/automatic.md), you can choose to automatically send a reminder email when a customer’s trial is about to expire.

Next, select the **Link to a Stripe-hosted page** option so the reminder email contains a link for the customer to add or update their payment details. We don’t send free trial reminder emails in a sandbox. Learn more about how to [set up free trial reminders](https://docs.stripe.com/billing/revenue-recovery/customer-emails#trial-ending-reminders.md).

You must comply with card network requirements when offering trials. Learn more about [compliance requirements for trials and promotions](https://docs.stripe.com/billing/subscriptions/trials#compliance.md).

### Collect payment details in the Billing customer portal

You can also send the reminder email yourself, and redirect customers to the Billing customer portal to add their payment details.

First, configure the [Billing customer portal](https://docs.stripe.com/customer-management.md) to enable your customers to manage their subscriptions.

Next, collect billing information from your customers:

1. Listen to the `customer.subscription.trial_will_end` [event](https://docs.stripe.com/api/events/types#event_types-customer.subscription.trial_will_end.md).
1. If the subscription doesn’t have a [default payment method](https://docs.stripe.com/api/subscriptions/object#subscription_object-default_payment_method.md), get the customer’s email using the [Customers API](https://docs.stripe.com/api/customers/retrieve.md) and send them a message with a link to your site. It’s helpful to embed the customer ID in the email, for example `https://example.com?...&customer={{CUSTOMER_ID}}`.
1. When the customer lands on your site, create a customer portal session using the customer ID from the previous step.
1. [Redirect](https://docs.stripe.com/customer-management/integrate-customer-portal#redirect.md) the customer to the customer portal, where they can update their subscription with payment details.

Your customers can also [resume their paused subscription](https://docs.stripe.com/billing/subscriptions/trials#resume-a-paused-subscription.md) in the customer portal by selecting **Start subscription**, then adding a payment method. View [free trial periods](https://docs.stripe.com/billing/subscriptions/trials#create-free-trials-without-payment.md) to learn how to configure a subscription to pause or cancel when a free trial ends without a payment method attached.


# Embedded form

> This is a Embedded form for when payment-ui is embedded-form. View the full page at https://docs.stripe.com/payments/checkout/free-trials?payment-ui=embedded-form.

You can configure a Checkout Session to start a customer’s subscription with a free trial by passing one of the following parameters:

- [subscription_data.trial_period_days](https://docs.stripe.com/api/checkout/sessions/create#create_checkout_session-subscription_data-trial_period_days.md), the length (in days) of your free trial.
- [subscription_data.trial_end](https://docs.stripe.com/api/checkout/sessions/create#create_checkout_session-subscription_data-trial_end.md), a Unix timestamp representing the end of the trial period.

```curl
curl https://api.stripe.com/v1/checkout/sessions \
  -u "<<YOUR_SECRET_KEY>>:" \
  -d mode=subscription \
  -d ui_mode=embedded \
  --data-urlencode return_url="https://example.com/return" \
  -d "line_items[0][price]"="{{PRICE_ID}}" \
  -d "line_items[0][quantity]"=1 \
  -d "subscription_data[trial_period_days]"=30
```

```cli
stripe checkout sessions create  \
  --mode=subscription \
  --ui-mode=embedded \
  --return-url="https://example.com/return" \
  -d "line_items[0][price]"="{{PRICE_ID}}" \
  -d "line_items[0][quantity]"=1 \
  -d "subscription_data[trial_period_days]"=30
```

```ruby
Stripe.api_key = '<<YOUR_SECRET_KEY>>'

session = Stripe::Checkout::Session.create({
  mode: 'subscription',
  ui_mode: 'embedded',
  return_url: 'https://example.com/return',
  line_items: [
    {
      price: '{{PRICE_ID}}',
      quantity: 1,
    },
  ],
  subscription_data: {trial_period_days: 30},
})
```

```ruby
client = Stripe::StripeClient.new("<<YOUR_SECRET_KEY>>")

session = client.v1.checkout.sessions.create({
  mode: 'subscription',
  ui_mode: 'embedded',
  return_url: 'https://example.com/return',
  line_items: [
    {
      price: '{{PRICE_ID}}',
      quantity: 1,
    },
  ],
  subscription_data: {trial_period_days: 30},
})
```

```python
import stripe
stripe.api_key = "<<YOUR_SECRET_KEY>>"

session = stripe.checkout.Session.create(
  mode="subscription",
  ui_mode="embedded",
  return_url="https://example.com/return",
  line_items=[{"price": "{{PRICE_ID}}", "quantity": 1}],
  subscription_data={"trial_period_days": 30},
)
```

```python
client = StripeClient("<<YOUR_SECRET_KEY>>")

session = client.checkout.sessions.create({
  "mode": "subscription",
  "ui_mode": "embedded",
  "return_url": "https://example.com/return",
  "line_items": [{"price": "{{PRICE_ID}}", "quantity": 1}],
  "subscription_data": {"trial_period_days": 30},
})
```

```php
$stripe = new \Stripe\StripeClient('<<YOUR_SECRET_KEY>>');

$session = $stripe->checkout->sessions->create([
  'mode' => 'subscription',
  'ui_mode' => 'embedded',
  'return_url' => 'https://example.com/return',
  'line_items' => [
    [
      'price' => '{{PRICE_ID}}',
      'quantity' => 1,
    ],
  ],
  'subscription_data' => ['trial_period_days' => 30],
]);
```

```java
Stripe.apiKey = "<<YOUR_SECRET_KEY>>";

SessionCreateParams params =
  SessionCreateParams.builder()
    .setMode(SessionCreateParams.Mode.SUBSCRIPTION)
    .setUiMode(SessionCreateParams.UiMode.EMBEDDED)
    .setReturnUrl("https://example.com/return")
    .addLineItem(
      SessionCreateParams.LineItem.builder()
        .setPrice("{{PRICE_ID}}")
        .setQuantity(1L)
        .build()
    )
    .setSubscriptionData(
      SessionCreateParams.SubscriptionData.builder().setTrialPeriodDays(30L).build()
    )
    .build();

Session session = Session.create(params);
```

```java
StripeClient client = new StripeClient("<<YOUR_SECRET_KEY>>");

SessionCreateParams params =
  SessionCreateParams.builder()
    .setMode(SessionCreateParams.Mode.SUBSCRIPTION)
    .setUiMode(SessionCreateParams.UiMode.EMBEDDED)
    .setReturnUrl("https://example.com/return")
    .addLineItem(
      SessionCreateParams.LineItem.builder()
        .setPrice("{{PRICE_ID}}")
        .setQuantity(1L)
        .build()
    )
    .setSubscriptionData(
      SessionCreateParams.SubscriptionData.builder().setTrialPeriodDays(30L).build()
    )
    .build();

Session session = client.checkout().sessions().create(params);
```

```node
const stripe = require('stripe')('<<YOUR_SECRET_KEY>>');

const session = await stripe.checkout.sessions.create({
  mode: 'subscription',
  ui_mode: 'embedded',
  return_url: 'https://example.com/return',
  line_items: [
    {
      price: '{{PRICE_ID}}',
      quantity: 1,
    },
  ],
  subscription_data: {
    trial_period_days: 30,
  },
});
```

```go
stripe.Key = "<<YOUR_SECRET_KEY>>"

params := &stripe.CheckoutSessionParams{
  Mode: stripe.String(stripe.CheckoutSessionModeSubscription),
  UIMode: stripe.String(stripe.CheckoutSessionUIModeEmbedded),
  ReturnURL: stripe.String("https://example.com/return"),
  LineItems: []*stripe.CheckoutSessionLineItemParams{
    &stripe.CheckoutSessionLineItemParams{
      Price: stripe.String("{{PRICE_ID}}"),
      Quantity: stripe.Int64(1),
    },
  },
  SubscriptionData: &stripe.CheckoutSessionSubscriptionDataParams{
    TrialPeriodDays: stripe.Int64(30),
  },
}
result, err := session.New(params)
```

```go
sc := stripe.NewClient("<<YOUR_SECRET_KEY>>")
params := &stripe.CheckoutSessionCreateParams{
  Mode: stripe.String(stripe.CheckoutSessionModeSubscription),
  UIMode: stripe.String(stripe.CheckoutSessionUIModeEmbedded),
  ReturnURL: stripe.String("https://example.com/return"),
  LineItems: []*stripe.CheckoutSessionCreateLineItemParams{
    &stripe.CheckoutSessionCreateLineItemParams{
      Price: stripe.String("{{PRICE_ID}}"),
      Quantity: stripe.Int64(1),
    },
  },
  SubscriptionData: &stripe.CheckoutSessionCreateSubscriptionDataParams{
    TrialPeriodDays: stripe.Int64(30),
  },
}
result, err := sc.V1CheckoutSessions.Create(context.TODO(), params)
```

```dotnet
StripeConfiguration.ApiKey = "<<YOUR_SECRET_KEY>>";

var options = new Stripe.Checkout.SessionCreateOptions
{
    Mode = "subscription",
    UiMode = "embedded",
    ReturnUrl = "https://example.com/return",
    LineItems = new List<Stripe.Checkout.SessionLineItemOptions>
    {
        new Stripe.Checkout.SessionLineItemOptions
        {
            Price = "{{PRICE_ID}}",
            Quantity = 1,
        },
    },
    SubscriptionData = new Stripe.Checkout.SessionSubscriptionDataOptions
    {
        TrialPeriodDays = 30,
    },
};
var service = new Stripe.Checkout.SessionService();
Stripe.Checkout.Session session = service.Create(options);
```

```dotnet
var options = new Stripe.Checkout.SessionCreateOptions
{
    Mode = "subscription",
    UiMode = "embedded",
    ReturnUrl = "https://example.com/return",
    LineItems = new List<Stripe.Checkout.SessionLineItemOptions>
    {
        new Stripe.Checkout.SessionLineItemOptions
        {
            Price = "{{PRICE_ID}}",
            Quantity = 1,
        },
    },
    SubscriptionData = new Stripe.Checkout.SessionSubscriptionDataOptions
    {
        TrialPeriodDays = 30,
    },
};
var client = new StripeClient("<<YOUR_SECRET_KEY>>");
var service = client.V1.Checkout.Sessions;
Stripe.Checkout.Session session = service.Create(options);
```

## Free trials without collecting a payment method 

By default, Checkout Sessions collect a payment method to use after the trial ends. You can sign customers up for free trials without collecting payment details by passing [payment_method_collection=if_required](https://docs.stripe.com/api/checkout/sessions/create#create_checkout_session-payment_method_collection.md).

Choose whether to cancel or pause the subscription if the customer doesn’t provide a payment method before the trial ends by passing [trial_settings.end_behavior.missing_payment_method](https://docs.stripe.com/api/checkout/sessions/create#create_checkout_session-subscription_data-trial_settings-end_behavior-missing_payment_method.md).

- **Cancel subscription**-If the free trial subscription ends without a payment method, it cancels immediately. You can create another subscription if the customer decides to subscribe to a paid plan in the future.
- **Pause subscription**-If the free trial subscription ends without a payment method, it pauses and doesn’t cycle until it’s resumed. When a subscription is paused, it doesn’t generate invoices (unlike when a subscription’s [payment collection](https://docs.stripe.com/billing/subscriptions/pause-payment.md) is paused). When your customer adds their payment method after the subscription has paused, you can resume the same subscription. The subscription can remain paused indefinitely.

```curl
curl https://api.stripe.com/v1/checkout/sessions \
  -u "<<YOUR_SECRET_KEY>>:" \
  -d mode=subscription \
  -d ui_mode=embedded \
  --data-urlencode return_url="https://example.com/return" \
  -d "line_items[0][price]"="{{PRICE_ID}}" \
  -d "line_items[0][quantity]"=1 \
  -d "subscription_data[trial_period_days]"=30 \
  -d "subscription_data[trial_settings][end_behavior][missing_payment_method]"=cancel \
  -d payment_method_collection=if_required
```

```cli
stripe checkout sessions create  \
  --mode=subscription \
  --ui-mode=embedded \
  --return-url="https://example.com/return" \
  -d "line_items[0][price]"="{{PRICE_ID}}" \
  -d "line_items[0][quantity]"=1 \
  -d "subscription_data[trial_period_days]"=30 \
  -d "subscription_data[trial_settings][end_behavior][missing_payment_method]"=cancel \
  --payment-method-collection=if_required
```

```ruby
Stripe.api_key = '<<YOUR_SECRET_KEY>>'

session = Stripe::Checkout::Session.create({
  mode: 'subscription',
  ui_mode: 'embedded',
  return_url: 'https://example.com/return',
  line_items: [
    {
      price: '{{PRICE_ID}}',
      quantity: 1,
    },
  ],
  subscription_data: {
    trial_period_days: 30,
    trial_settings: {end_behavior: {missing_payment_method: 'cancel'}},
  },
  payment_method_collection: 'if_required',
})
```

```ruby
client = Stripe::StripeClient.new("<<YOUR_SECRET_KEY>>")

session = client.v1.checkout.sessions.create({
  mode: 'subscription',
  ui_mode: 'embedded',
  return_url: 'https://example.com/return',
  line_items: [
    {
      price: '{{PRICE_ID}}',
      quantity: 1,
    },
  ],
  subscription_data: {
    trial_period_days: 30,
    trial_settings: {end_behavior: {missing_payment_method: 'cancel'}},
  },
  payment_method_collection: 'if_required',
})
```

```python
import stripe
stripe.api_key = "<<YOUR_SECRET_KEY>>"

session = stripe.checkout.Session.create(
  mode="subscription",
  ui_mode="embedded",
  return_url="https://example.com/return",
  line_items=[{"price": "{{PRICE_ID}}", "quantity": 1}],
  subscription_data={
    "trial_period_days": 30,
    "trial_settings": {"end_behavior": {"missing_payment_method": "cancel"}},
  },
  payment_method_collection="if_required",
)
```

```python
client = StripeClient("<<YOUR_SECRET_KEY>>")

session = client.checkout.sessions.create({
  "mode": "subscription",
  "ui_mode": "embedded",
  "return_url": "https://example.com/return",
  "line_items": [{"price": "{{PRICE_ID}}", "quantity": 1}],
  "subscription_data": {
    "trial_period_days": 30,
    "trial_settings": {"end_behavior": {"missing_payment_method": "cancel"}},
  },
  "payment_method_collection": "if_required",
})
```

```php
$stripe = new \Stripe\StripeClient('<<YOUR_SECRET_KEY>>');

$session = $stripe->checkout->sessions->create([
  'mode' => 'subscription',
  'ui_mode' => 'embedded',
  'return_url' => 'https://example.com/return',
  'line_items' => [
    [
      'price' => '{{PRICE_ID}}',
      'quantity' => 1,
    ],
  ],
  'subscription_data' => [
    'trial_period_days' => 30,
    'trial_settings' => ['end_behavior' => ['missing_payment_method' => 'cancel']],
  ],
  'payment_method_collection' => 'if_required',
]);
```

```java
Stripe.apiKey = "<<YOUR_SECRET_KEY>>";

SessionCreateParams params =
  SessionCreateParams.builder()
    .setMode(SessionCreateParams.Mode.SUBSCRIPTION)
    .setUiMode(SessionCreateParams.UiMode.EMBEDDED)
    .setReturnUrl("https://example.com/return")
    .addLineItem(
      SessionCreateParams.LineItem.builder()
        .setPrice("{{PRICE_ID}}")
        .setQuantity(1L)
        .build()
    )
    .setSubscriptionData(
      SessionCreateParams.SubscriptionData.builder()
        .setTrialPeriodDays(30L)
        .setTrialSettings(
          SessionCreateParams.SubscriptionData.TrialSettings.builder()
            .setEndBehavior(
              SessionCreateParams.SubscriptionData.TrialSettings.EndBehavior.builder()
                .setMissingPaymentMethod(
                  SessionCreateParams.SubscriptionData.TrialSettings.EndBehavior.MissingPaymentMethod.CANCEL
                )
                .build()
            )
            .build()
        )
        .build()
    )
    .setPaymentMethodCollection(SessionCreateParams.PaymentMethodCollection.IF_REQUIRED)
    .build();

Session session = Session.create(params);
```

```java
StripeClient client = new StripeClient("<<YOUR_SECRET_KEY>>");

SessionCreateParams params =
  SessionCreateParams.builder()
    .setMode(SessionCreateParams.Mode.SUBSCRIPTION)
    .setUiMode(SessionCreateParams.UiMode.EMBEDDED)
    .setReturnUrl("https://example.com/return")
    .addLineItem(
      SessionCreateParams.LineItem.builder()
        .setPrice("{{PRICE_ID}}")
        .setQuantity(1L)
        .build()
    )
    .setSubscriptionData(
      SessionCreateParams.SubscriptionData.builder()
        .setTrialPeriodDays(30L)
        .setTrialSettings(
          SessionCreateParams.SubscriptionData.TrialSettings.builder()
            .setEndBehavior(
              SessionCreateParams.SubscriptionData.TrialSettings.EndBehavior.builder()
                .setMissingPaymentMethod(
                  SessionCreateParams.SubscriptionData.TrialSettings.EndBehavior.MissingPaymentMethod.CANCEL
                )
                .build()
            )
            .build()
        )
        .build()
    )
    .setPaymentMethodCollection(SessionCreateParams.PaymentMethodCollection.IF_REQUIRED)
    .build();

Session session = client.checkout().sessions().create(params);
```

```node
const stripe = require('stripe')('<<YOUR_SECRET_KEY>>');

const session = await stripe.checkout.sessions.create({
  mode: 'subscription',
  ui_mode: 'embedded',
  return_url: 'https://example.com/return',
  line_items: [
    {
      price: '{{PRICE_ID}}',
      quantity: 1,
    },
  ],
  subscription_data: {
    trial_period_days: 30,
    trial_settings: {
      end_behavior: {
        missing_payment_method: 'cancel',
      },
    },
  },
  payment_method_collection: 'if_required',
});
```

```go
stripe.Key = "<<YOUR_SECRET_KEY>>"

params := &stripe.CheckoutSessionParams{
  Mode: stripe.String(stripe.CheckoutSessionModeSubscription),
  UIMode: stripe.String(stripe.CheckoutSessionUIModeEmbedded),
  ReturnURL: stripe.String("https://example.com/return"),
  LineItems: []*stripe.CheckoutSessionLineItemParams{
    &stripe.CheckoutSessionLineItemParams{
      Price: stripe.String("{{PRICE_ID}}"),
      Quantity: stripe.Int64(1),
    },
  },
  SubscriptionData: &stripe.CheckoutSessionSubscriptionDataParams{
    TrialPeriodDays: stripe.Int64(30),
    TrialSettings: &stripe.CheckoutSessionSubscriptionDataTrialSettingsParams{
      EndBehavior: &stripe.CheckoutSessionSubscriptionDataTrialSettingsEndBehaviorParams{
        MissingPaymentMethod: stripe.String("cancel"),
      },
    },
  },
  PaymentMethodCollection: stripe.String(stripe.CheckoutSessionPaymentMethodCollectionIfRequired),
}
result, err := session.New(params)
```

```go
sc := stripe.NewClient("<<YOUR_SECRET_KEY>>")
params := &stripe.CheckoutSessionCreateParams{
  Mode: stripe.String(stripe.CheckoutSessionModeSubscription),
  UIMode: stripe.String(stripe.CheckoutSessionUIModeEmbedded),
  ReturnURL: stripe.String("https://example.com/return"),
  LineItems: []*stripe.CheckoutSessionCreateLineItemParams{
    &stripe.CheckoutSessionCreateLineItemParams{
      Price: stripe.String("{{PRICE_ID}}"),
      Quantity: stripe.Int64(1),
    },
  },
  SubscriptionData: &stripe.CheckoutSessionCreateSubscriptionDataParams{
    TrialPeriodDays: stripe.Int64(30),
    TrialSettings: &stripe.CheckoutSessionCreateSubscriptionDataTrialSettingsParams{
      EndBehavior: &stripe.CheckoutSessionCreateSubscriptionDataTrialSettingsEndBehaviorParams{
        MissingPaymentMethod: stripe.String("cancel"),
      },
    },
  },
  PaymentMethodCollection: stripe.String(stripe.CheckoutSessionPaymentMethodCollectionIfRequired),
}
result, err := sc.V1CheckoutSessions.Create(context.TODO(), params)
```

```dotnet
StripeConfiguration.ApiKey = "<<YOUR_SECRET_KEY>>";

var options = new Stripe.Checkout.SessionCreateOptions
{
    Mode = "subscription",
    UiMode = "embedded",
    ReturnUrl = "https://example.com/return",
    LineItems = new List<Stripe.Checkout.SessionLineItemOptions>
    {
        new Stripe.Checkout.SessionLineItemOptions
        {
            Price = "{{PRICE_ID}}",
            Quantity = 1,
        },
    },
    SubscriptionData = new Stripe.Checkout.SessionSubscriptionDataOptions
    {
        TrialPeriodDays = 30,
        TrialSettings = new Stripe.Checkout.SessionSubscriptionDataTrialSettingsOptions
        {
            EndBehavior = new Stripe.Checkout.SessionSubscriptionDataTrialSettingsEndBehaviorOptions
            {
                MissingPaymentMethod = "cancel",
            },
        },
    },
    PaymentMethodCollection = "if_required",
};
var service = new Stripe.Checkout.SessionService();
Stripe.Checkout.Session session = service.Create(options);
```

```dotnet
var options = new Stripe.Checkout.SessionCreateOptions
{
    Mode = "subscription",
    UiMode = "embedded",
    ReturnUrl = "https://example.com/return",
    LineItems = new List<Stripe.Checkout.SessionLineItemOptions>
    {
        new Stripe.Checkout.SessionLineItemOptions
        {
            Price = "{{PRICE_ID}}",
            Quantity = 1,
        },
    },
    SubscriptionData = new Stripe.Checkout.SessionSubscriptionDataOptions
    {
        TrialPeriodDays = 30,
        TrialSettings = new Stripe.Checkout.SessionSubscriptionDataTrialSettingsOptions
        {
            EndBehavior = new Stripe.Checkout.SessionSubscriptionDataTrialSettingsEndBehaviorOptions
            {
                MissingPaymentMethod = "cancel",
            },
        },
    },
    PaymentMethodCollection = "if_required",
};
var client = new StripeClient("<<YOUR_SECRET_KEY>>");
var service = client.V1.Checkout.Sessions;
Stripe.Checkout.Session session = service.Create(options);
```

### Collect payment details automatically 

Before the trial expires, collect payment details from your customer.

Under **Manage free trial messaging** in your [Subscriptions and emails settings](https://dashboard.stripe.com/settings/billing/automatic.md), you can choose to automatically send a reminder email when a customer’s trial is about to expire.

Next, select the **Link to a Stripe-hosted page** option so the reminder email contains a link for the customer to add or update their payment details. We don’t send free trial reminder emails in a sandbox. Learn more about how to [set up free trial reminders](https://docs.stripe.com/billing/revenue-recovery/customer-emails#trial-ending-reminders.md).

You must comply with card network requirements when offering trials. Learn more about [compliance requirements for trials and promotions](https://docs.stripe.com/billing/subscriptions/trials#compliance.md).

### Collect payment details in the Billing customer portal

You can also send the reminder email yourself, and redirect customers to the Billing customer portal to add their payment details.

First, configure the [Billing customer portal](https://docs.stripe.com/customer-management.md) to enable your customers to manage their subscriptions.

Next, collect billing information from your customers:

1. Listen to the `customer.subscription.trial_will_end` [event](https://docs.stripe.com/api/events/types#event_types-customer.subscription.trial_will_end.md).
1. If the subscription doesn’t have a [default payment method](https://docs.stripe.com/api/subscriptions/object#subscription_object-default_payment_method.md), get the customer’s email using the [Customers API](https://docs.stripe.com/api/customers/retrieve.md) and send them a message with a link to your site. It’s helpful to embed the customer ID in the email, for example `https://example.com?...&customer={{CUSTOMER_ID}}`.
1. When the customer lands on your site, create a customer portal session using the customer ID from the previous step.
1. [Redirect](https://docs.stripe.com/customer-management/integrate-customer-portal#redirect.md) the customer to the customer portal, where they can update their subscription with payment details.

Your customers can also [resume their paused subscription](https://docs.stripe.com/billing/subscriptions/trials#resume-a-paused-subscription.md) in the customer portal by selecting **Start subscription**, then adding a payment method. View [free trial periods](https://docs.stripe.com/billing/subscriptions/trials#create-free-trials-without-payment.md) to learn how to configure a subscription to pause or cancel when a free trial ends without a payment method attached.


# Embedded components

> This is a Embedded components for when payment-ui is embedded-components. View the full page at https://docs.stripe.com/payments/checkout/free-trials?payment-ui=embedded-components.

You can configure a Checkout Session to start a customer’s subscription with a free trial by passing one of the following parameters:

- [subscription_data.trial_period_days](https://docs.stripe.com/api/checkout/sessions/create#create_checkout_session-subscription_data-trial_period_days.md), the length (in days) of your free trial.
- [subscription_data.trial_end](https://docs.stripe.com/api/checkout/sessions/create#create_checkout_session-subscription_data-trial_end.md), a Unix timestamp representing the end of the trial period.

```curl
curl https://api.stripe.com/v1/checkout/sessions \
  -u "<<YOUR_SECRET_KEY>>:" \
  -d mode=subscription \
  -d ui_mode=custom \
  --data-urlencode return_url="https://example.com/return" \
  -d "line_items[0][price]"="{{PRICE_ID}}" \
  -d "line_items[0][quantity]"=1 \
  -d "subscription_data[trial_period_days]"=30
```

```cli
stripe checkout sessions create  \
  --mode=subscription \
  --ui-mode=custom \
  --return-url="https://example.com/return" \
  -d "line_items[0][price]"="{{PRICE_ID}}" \
  -d "line_items[0][quantity]"=1 \
  -d "subscription_data[trial_period_days]"=30
```

```ruby
Stripe.api_key = '<<YOUR_SECRET_KEY>>'

session = Stripe::Checkout::Session.create({
  mode: 'subscription',
  ui_mode: 'custom',
  return_url: 'https://example.com/return',
  line_items: [
    {
      price: '{{PRICE_ID}}',
      quantity: 1,
    },
  ],
  subscription_data: {trial_period_days: 30},
})
```

```ruby
client = Stripe::StripeClient.new("<<YOUR_SECRET_KEY>>")

session = client.v1.checkout.sessions.create({
  mode: 'subscription',
  ui_mode: 'custom',
  return_url: 'https://example.com/return',
  line_items: [
    {
      price: '{{PRICE_ID}}',
      quantity: 1,
    },
  ],
  subscription_data: {trial_period_days: 30},
})
```

```python
import stripe
stripe.api_key = "<<YOUR_SECRET_KEY>>"

session = stripe.checkout.Session.create(
  mode="subscription",
  ui_mode="custom",
  return_url="https://example.com/return",
  line_items=[{"price": "{{PRICE_ID}}", "quantity": 1}],
  subscription_data={"trial_period_days": 30},
)
```

```python
client = StripeClient("<<YOUR_SECRET_KEY>>")

session = client.checkout.sessions.create({
  "mode": "subscription",
  "ui_mode": "custom",
  "return_url": "https://example.com/return",
  "line_items": [{"price": "{{PRICE_ID}}", "quantity": 1}],
  "subscription_data": {"trial_period_days": 30},
})
```

```php
$stripe = new \Stripe\StripeClient('<<YOUR_SECRET_KEY>>');

$session = $stripe->checkout->sessions->create([
  'mode' => 'subscription',
  'ui_mode' => 'custom',
  'return_url' => 'https://example.com/return',
  'line_items' => [
    [
      'price' => '{{PRICE_ID}}',
      'quantity' => 1,
    ],
  ],
  'subscription_data' => ['trial_period_days' => 30],
]);
```

```java
Stripe.apiKey = "<<YOUR_SECRET_KEY>>";

SessionCreateParams params =
  SessionCreateParams.builder()
    .setMode(SessionCreateParams.Mode.SUBSCRIPTION)
    .setUiMode(SessionCreateParams.UiMode.CUSTOM)
    .setReturnUrl("https://example.com/return")
    .addLineItem(
      SessionCreateParams.LineItem.builder()
        .setPrice("{{PRICE_ID}}")
        .setQuantity(1L)
        .build()
    )
    .setSubscriptionData(
      SessionCreateParams.SubscriptionData.builder().setTrialPeriodDays(30L).build()
    )
    .build();

Session session = Session.create(params);
```

```java
StripeClient client = new StripeClient("<<YOUR_SECRET_KEY>>");

SessionCreateParams params =
  SessionCreateParams.builder()
    .setMode(SessionCreateParams.Mode.SUBSCRIPTION)
    .setUiMode(SessionCreateParams.UiMode.CUSTOM)
    .setReturnUrl("https://example.com/return")
    .addLineItem(
      SessionCreateParams.LineItem.builder()
        .setPrice("{{PRICE_ID}}")
        .setQuantity(1L)
        .build()
    )
    .setSubscriptionData(
      SessionCreateParams.SubscriptionData.builder().setTrialPeriodDays(30L).build()
    )
    .build();

Session session = client.checkout().sessions().create(params);
```

```node
const stripe = require('stripe')('<<YOUR_SECRET_KEY>>');

const session = await stripe.checkout.sessions.create({
  mode: 'subscription',
  ui_mode: 'custom',
  return_url: 'https://example.com/return',
  line_items: [
    {
      price: '{{PRICE_ID}}',
      quantity: 1,
    },
  ],
  subscription_data: {
    trial_period_days: 30,
  },
});
```

```go
stripe.Key = "<<YOUR_SECRET_KEY>>"

params := &stripe.CheckoutSessionParams{
  Mode: stripe.String(stripe.CheckoutSessionModeSubscription),
  UIMode: stripe.String(stripe.CheckoutSessionUIModeCustom),
  ReturnURL: stripe.String("https://example.com/return"),
  LineItems: []*stripe.CheckoutSessionLineItemParams{
    &stripe.CheckoutSessionLineItemParams{
      Price: stripe.String("{{PRICE_ID}}"),
      Quantity: stripe.Int64(1),
    },
  },
  SubscriptionData: &stripe.CheckoutSessionSubscriptionDataParams{
    TrialPeriodDays: stripe.Int64(30),
  },
}
result, err := session.New(params)
```

```go
sc := stripe.NewClient("<<YOUR_SECRET_KEY>>")
params := &stripe.CheckoutSessionCreateParams{
  Mode: stripe.String(stripe.CheckoutSessionModeSubscription),
  UIMode: stripe.String(stripe.CheckoutSessionUIModeCustom),
  ReturnURL: stripe.String("https://example.com/return"),
  LineItems: []*stripe.CheckoutSessionCreateLineItemParams{
    &stripe.CheckoutSessionCreateLineItemParams{
      Price: stripe.String("{{PRICE_ID}}"),
      Quantity: stripe.Int64(1),
    },
  },
  SubscriptionData: &stripe.CheckoutSessionCreateSubscriptionDataParams{
    TrialPeriodDays: stripe.Int64(30),
  },
}
result, err := sc.V1CheckoutSessions.Create(context.TODO(), params)
```

```dotnet
StripeConfiguration.ApiKey = "<<YOUR_SECRET_KEY>>";

var options = new Stripe.Checkout.SessionCreateOptions
{
    Mode = "subscription",
    UiMode = "custom",
    ReturnUrl = "https://example.com/return",
    LineItems = new List<Stripe.Checkout.SessionLineItemOptions>
    {
        new Stripe.Checkout.SessionLineItemOptions
        {
            Price = "{{PRICE_ID}}",
            Quantity = 1,
        },
    },
    SubscriptionData = new Stripe.Checkout.SessionSubscriptionDataOptions
    {
        TrialPeriodDays = 30,
    },
};
var service = new Stripe.Checkout.SessionService();
Stripe.Checkout.Session session = service.Create(options);
```

```dotnet
var options = new Stripe.Checkout.SessionCreateOptions
{
    Mode = "subscription",
    UiMode = "custom",
    ReturnUrl = "https://example.com/return",
    LineItems = new List<Stripe.Checkout.SessionLineItemOptions>
    {
        new Stripe.Checkout.SessionLineItemOptions
        {
            Price = "{{PRICE_ID}}",
            Quantity = 1,
        },
    },
    SubscriptionData = new Stripe.Checkout.SessionSubscriptionDataOptions
    {
        TrialPeriodDays = 30,
    },
};
var client = new StripeClient("<<YOUR_SECRET_KEY>>");
var service = client.V1.Checkout.Sessions;
Stripe.Checkout.Session session = service.Create(options);
```

On the front end, [recurring.trial](https://docs.stripe.com/js/custom_checkout/session_object#custom_checkout_session_object-recurring-trial.md) contains the details of the session’s trial period.

## Free trials without collecting a payment method 

By default, Checkout Sessions collect a payment method to use after the trial ends. You can sign customers up for free trials without collecting payment details by passing [payment_method_collection=if_required](https://docs.stripe.com/api/checkout/sessions/create#create_checkout_session-payment_method_collection.md).

Choose whether to cancel or pause the subscription if the customer doesn’t provide a payment method before the trial ends by passing [trial_settings.end_behavior.missing_payment_method](https://docs.stripe.com/api/checkout/sessions/create#create_checkout_session-subscription_data-trial_settings-end_behavior-missing_payment_method.md).

- **Cancel subscription**-If the free trial subscription ends without a payment method, it cancels immediately. You can create another subscription if the customer decides to subscribe to a paid plan in the future.
- **Pause subscription**-If the free trial subscription ends without a payment method, it pauses and doesn’t cycle until it’s resumed. When a subscription is paused, it doesn’t generate invoices (unlike when a subscription’s [payment collection](https://docs.stripe.com/billing/subscriptions/pause-payment.md) is paused). When your customer adds their payment method after the subscription has paused, you can resume the same subscription. The subscription can remain paused indefinitely.

```curl
curl https://api.stripe.com/v1/checkout/sessions \
  -u "<<YOUR_SECRET_KEY>>:" \
  -d mode=subscription \
  -d ui_mode=custom \
  --data-urlencode return_url="https://example.com/return" \
  -d "line_items[0][price]"="{{PRICE_ID}}" \
  -d "line_items[0][quantity]"=1 \
  -d "subscription_data[trial_period_days]"=30 \
  -d "subscription_data[trial_settings][end_behavior][missing_payment_method]"=cancel \
  -d payment_method_collection=if_required
```

```cli
stripe checkout sessions create  \
  --mode=subscription \
  --ui-mode=custom \
  --return-url="https://example.com/return" \
  -d "line_items[0][price]"="{{PRICE_ID}}" \
  -d "line_items[0][quantity]"=1 \
  -d "subscription_data[trial_period_days]"=30 \
  -d "subscription_data[trial_settings][end_behavior][missing_payment_method]"=cancel \
  --payment-method-collection=if_required
```

```ruby
Stripe.api_key = '<<YOUR_SECRET_KEY>>'

session = Stripe::Checkout::Session.create({
  mode: 'subscription',
  ui_mode: 'custom',
  return_url: 'https://example.com/return',
  line_items: [
    {
      price: '{{PRICE_ID}}',
      quantity: 1,
    },
  ],
  subscription_data: {
    trial_period_days: 30,
    trial_settings: {end_behavior: {missing_payment_method: 'cancel'}},
  },
  payment_method_collection: 'if_required',
})
```

```ruby
client = Stripe::StripeClient.new("<<YOUR_SECRET_KEY>>")

session = client.v1.checkout.sessions.create({
  mode: 'subscription',
  ui_mode: 'custom',
  return_url: 'https://example.com/return',
  line_items: [
    {
      price: '{{PRICE_ID}}',
      quantity: 1,
    },
  ],
  subscription_data: {
    trial_period_days: 30,
    trial_settings: {end_behavior: {missing_payment_method: 'cancel'}},
  },
  payment_method_collection: 'if_required',
})
```

```python
import stripe
stripe.api_key = "<<YOUR_SECRET_KEY>>"

session = stripe.checkout.Session.create(
  mode="subscription",
  ui_mode="custom",
  return_url="https://example.com/return",
  line_items=[{"price": "{{PRICE_ID}}", "quantity": 1}],
  subscription_data={
    "trial_period_days": 30,
    "trial_settings": {"end_behavior": {"missing_payment_method": "cancel"}},
  },
  payment_method_collection="if_required",
)
```

```python
client = StripeClient("<<YOUR_SECRET_KEY>>")

session = client.checkout.sessions.create({
  "mode": "subscription",
  "ui_mode": "custom",
  "return_url": "https://example.com/return",
  "line_items": [{"price": "{{PRICE_ID}}", "quantity": 1}],
  "subscription_data": {
    "trial_period_days": 30,
    "trial_settings": {"end_behavior": {"missing_payment_method": "cancel"}},
  },
  "payment_method_collection": "if_required",
})
```

```php
$stripe = new \Stripe\StripeClient('<<YOUR_SECRET_KEY>>');

$session = $stripe->checkout->sessions->create([
  'mode' => 'subscription',
  'ui_mode' => 'custom',
  'return_url' => 'https://example.com/return',
  'line_items' => [
    [
      'price' => '{{PRICE_ID}}',
      'quantity' => 1,
    ],
  ],
  'subscription_data' => [
    'trial_period_days' => 30,
    'trial_settings' => ['end_behavior' => ['missing_payment_method' => 'cancel']],
  ],
  'payment_method_collection' => 'if_required',
]);
```

```java
Stripe.apiKey = "<<YOUR_SECRET_KEY>>";

SessionCreateParams params =
  SessionCreateParams.builder()
    .setMode(SessionCreateParams.Mode.SUBSCRIPTION)
    .setUiMode(SessionCreateParams.UiMode.CUSTOM)
    .setReturnUrl("https://example.com/return")
    .addLineItem(
      SessionCreateParams.LineItem.builder()
        .setPrice("{{PRICE_ID}}")
        .setQuantity(1L)
        .build()
    )
    .setSubscriptionData(
      SessionCreateParams.SubscriptionData.builder()
        .setTrialPeriodDays(30L)
        .setTrialSettings(
          SessionCreateParams.SubscriptionData.TrialSettings.builder()
            .setEndBehavior(
              SessionCreateParams.SubscriptionData.TrialSettings.EndBehavior.builder()
                .setMissingPaymentMethod(
                  SessionCreateParams.SubscriptionData.TrialSettings.EndBehavior.MissingPaymentMethod.CANCEL
                )
                .build()
            )
            .build()
        )
        .build()
    )
    .setPaymentMethodCollection(SessionCreateParams.PaymentMethodCollection.IF_REQUIRED)
    .build();

Session session = Session.create(params);
```

```java
StripeClient client = new StripeClient("<<YOUR_SECRET_KEY>>");

SessionCreateParams params =
  SessionCreateParams.builder()
    .setMode(SessionCreateParams.Mode.SUBSCRIPTION)
    .setUiMode(SessionCreateParams.UiMode.CUSTOM)
    .setReturnUrl("https://example.com/return")
    .addLineItem(
      SessionCreateParams.LineItem.builder()
        .setPrice("{{PRICE_ID}}")
        .setQuantity(1L)
        .build()
    )
    .setSubscriptionData(
      SessionCreateParams.SubscriptionData.builder()
        .setTrialPeriodDays(30L)
        .setTrialSettings(
          SessionCreateParams.SubscriptionData.TrialSettings.builder()
            .setEndBehavior(
              SessionCreateParams.SubscriptionData.TrialSettings.EndBehavior.builder()
                .setMissingPaymentMethod(
                  SessionCreateParams.SubscriptionData.TrialSettings.EndBehavior.MissingPaymentMethod.CANCEL
                )
                .build()
            )
            .build()
        )
        .build()
    )
    .setPaymentMethodCollection(SessionCreateParams.PaymentMethodCollection.IF_REQUIRED)
    .build();

Session session = client.checkout().sessions().create(params);
```

```node
const stripe = require('stripe')('<<YOUR_SECRET_KEY>>');

const session = await stripe.checkout.sessions.create({
  mode: 'subscription',
  ui_mode: 'custom',
  return_url: 'https://example.com/return',
  line_items: [
    {
      price: '{{PRICE_ID}}',
      quantity: 1,
    },
  ],
  subscription_data: {
    trial_period_days: 30,
    trial_settings: {
      end_behavior: {
        missing_payment_method: 'cancel',
      },
    },
  },
  payment_method_collection: 'if_required',
});
```

```go
stripe.Key = "<<YOUR_SECRET_KEY>>"

params := &stripe.CheckoutSessionParams{
  Mode: stripe.String(stripe.CheckoutSessionModeSubscription),
  UIMode: stripe.String(stripe.CheckoutSessionUIModeCustom),
  ReturnURL: stripe.String("https://example.com/return"),
  LineItems: []*stripe.CheckoutSessionLineItemParams{
    &stripe.CheckoutSessionLineItemParams{
      Price: stripe.String("{{PRICE_ID}}"),
      Quantity: stripe.Int64(1),
    },
  },
  SubscriptionData: &stripe.CheckoutSessionSubscriptionDataParams{
    TrialPeriodDays: stripe.Int64(30),
    TrialSettings: &stripe.CheckoutSessionSubscriptionDataTrialSettingsParams{
      EndBehavior: &stripe.CheckoutSessionSubscriptionDataTrialSettingsEndBehaviorParams{
        MissingPaymentMethod: stripe.String("cancel"),
      },
    },
  },
  PaymentMethodCollection: stripe.String(stripe.CheckoutSessionPaymentMethodCollectionIfRequired),
}
result, err := session.New(params)
```

```go
sc := stripe.NewClient("<<YOUR_SECRET_KEY>>")
params := &stripe.CheckoutSessionCreateParams{
  Mode: stripe.String(stripe.CheckoutSessionModeSubscription),
  UIMode: stripe.String(stripe.CheckoutSessionUIModeCustom),
  ReturnURL: stripe.String("https://example.com/return"),
  LineItems: []*stripe.CheckoutSessionCreateLineItemParams{
    &stripe.CheckoutSessionCreateLineItemParams{
      Price: stripe.String("{{PRICE_ID}}"),
      Quantity: stripe.Int64(1),
    },
  },
  SubscriptionData: &stripe.CheckoutSessionCreateSubscriptionDataParams{
    TrialPeriodDays: stripe.Int64(30),
    TrialSettings: &stripe.CheckoutSessionCreateSubscriptionDataTrialSettingsParams{
      EndBehavior: &stripe.CheckoutSessionCreateSubscriptionDataTrialSettingsEndBehaviorParams{
        MissingPaymentMethod: stripe.String("cancel"),
      },
    },
  },
  PaymentMethodCollection: stripe.String(stripe.CheckoutSessionPaymentMethodCollectionIfRequired),
}
result, err := sc.V1CheckoutSessions.Create(context.TODO(), params)
```

```dotnet
StripeConfiguration.ApiKey = "<<YOUR_SECRET_KEY>>";

var options = new Stripe.Checkout.SessionCreateOptions
{
    Mode = "subscription",
    UiMode = "custom",
    ReturnUrl = "https://example.com/return",
    LineItems = new List<Stripe.Checkout.SessionLineItemOptions>
    {
        new Stripe.Checkout.SessionLineItemOptions
        {
            Price = "{{PRICE_ID}}",
            Quantity = 1,
        },
    },
    SubscriptionData = new Stripe.Checkout.SessionSubscriptionDataOptions
    {
        TrialPeriodDays = 30,
        TrialSettings = new Stripe.Checkout.SessionSubscriptionDataTrialSettingsOptions
        {
            EndBehavior = new Stripe.Checkout.SessionSubscriptionDataTrialSettingsEndBehaviorOptions
            {
                MissingPaymentMethod = "cancel",
            },
        },
    },
    PaymentMethodCollection = "if_required",
};
var service = new Stripe.Checkout.SessionService();
Stripe.Checkout.Session session = service.Create(options);
```

```dotnet
var options = new Stripe.Checkout.SessionCreateOptions
{
    Mode = "subscription",
    UiMode = "custom",
    ReturnUrl = "https://example.com/return",
    LineItems = new List<Stripe.Checkout.SessionLineItemOptions>
    {
        new Stripe.Checkout.SessionLineItemOptions
        {
            Price = "{{PRICE_ID}}",
            Quantity = 1,
        },
    },
    SubscriptionData = new Stripe.Checkout.SessionSubscriptionDataOptions
    {
        TrialPeriodDays = 30,
        TrialSettings = new Stripe.Checkout.SessionSubscriptionDataTrialSettingsOptions
        {
            EndBehavior = new Stripe.Checkout.SessionSubscriptionDataTrialSettingsEndBehaviorOptions
            {
                MissingPaymentMethod = "cancel",
            },
        },
    },
    PaymentMethodCollection = "if_required",
};
var client = new StripeClient("<<YOUR_SECRET_KEY>>");
var service = client.V1.Checkout.Sessions;
Stripe.Checkout.Session session = service.Create(options);
```

### Collect payment details automatically 

Before the trial expires, collect payment details from your customer.

Under **Manage free trial messaging** in your [Subscriptions and emails settings](https://dashboard.stripe.com/settings/billing/automatic.md), you can choose to automatically send a reminder email when a customer’s trial is about to expire.

Next, select the **Link to a Stripe-hosted page** option so the reminder email contains a link for the customer to add or update their payment details. We don’t send free trial reminder emails in a sandbox. Learn more about how to [set up free trial reminders](https://docs.stripe.com/billing/revenue-recovery/customer-emails#trial-ending-reminders.md).

You must comply with card network requirements when offering trials. Learn more about [compliance requirements for trials and promotions](https://docs.stripe.com/billing/subscriptions/trials#compliance.md).

### Collect payment details in the Billing customer portal

You can also send the reminder email yourself, and redirect customers to the Billing customer portal to add their payment details.

First, configure the [Billing customer portal](https://docs.stripe.com/customer-management.md) to enable your customers to manage their subscriptions.

Next, collect billing information from your customers:

1. Listen to the `customer.subscription.trial_will_end` [event](https://docs.stripe.com/api/events/types#event_types-customer.subscription.trial_will_end.md).
1. If the subscription doesn’t have a [default payment method](https://docs.stripe.com/api/subscriptions/object#subscription_object-default_payment_method.md), get the customer’s email using the [Customers API](https://docs.stripe.com/api/customers/retrieve.md) and send them a message with a link to your site. It’s helpful to embed the customer ID in the email, for example `https://example.com?...&customer={{CUSTOMER_ID}}`.
1. When the customer lands on your site, create a customer portal session using the customer ID from the previous step.
1. [Redirect](https://docs.stripe.com/customer-management/integrate-customer-portal#redirect.md) the customer to the customer portal, where they can update their subscription with payment details.

Your customers can also [resume their paused subscription](https://docs.stripe.com/billing/subscriptions/trials#resume-a-paused-subscription.md) in the customer portal by selecting **Start subscription**, then adding a payment method. View [free trial periods](https://docs.stripe.com/billing/subscriptions/trials#create-free-trials-without-payment.md) to learn how to configure a subscription to pause or cancel when a free trial ends without a payment method attached.


## See also

- [Using trial periods on subscriptions](https://docs.stripe.com/billing/subscriptions/trials.md)
- [Send an email reminder before the trial ends](https://docs.stripe.com/billing/subscriptions/trials#send-an-email-reminder-before-the-trial-ends.md)
- [Combine trials with usage-based billing](https://docs.stripe.com/billing/subscriptions/trials#trials-usage-based-billing.md)