'use client';

import { useMemo } from 'react';
import { useQuery as useZeroQuery } from '@rocicorp/zero/react';
import { useTeamAccountWorkspace } from '@kit/team-accounts/hooks/use-team-account-workspace';
import { useZero } from './use-zero';

export interface TrialStatus {
  hasActiveSubscription: boolean;
  isTrialing: boolean;
  trialExpired: boolean;
  trialExpiringSoon: boolean;
  canAccessApp: boolean;
  daysUntilExpiration: number | null;
  isLoading: boolean;
}

export interface TrialBadgeStatus {
  show: boolean;
  variant: 'success' | 'warning' | 'destructive';
  message: string;
  isLoading: boolean;
}

export function useTrialStatus(): TrialStatus {
  const workspace = useTeamAccountWorkspace();
  const zero = useZero();
  
  const [subscriptions, result] = useZeroQuery(
    zero.query.subscriptions.where('account_id', '=', workspace.account.id),
    { ttl: '5m' }
  );

  return useMemo(() => {
    const isLoading = result.type === 'unknown';

    if (isLoading) {
      return {
        hasActiveSubscription: false,
        isTrialing: false,
        trialExpired: false,
        trialExpiringSoon: false,
        canAccessApp: false, // Block access during loading to prevent content flash
        daysUntilExpiration: null,
        isLoading: true,
      };
    }

    const subscription = subscriptions?.find(sub => sub.active) || subscriptions?.[0];
    
    if (!subscription) {
      return {
        hasActiveSubscription: false,
        isTrialing: false,
        trialExpired: false,
        trialExpiringSoon: false,
        canAccessApp: false,
        daysUntilExpiration: null,
        isLoading: false,
      };
    }

    const isTrialing = subscription.status === 'trialing';
    const hasActiveSubscription = subscription.status === 'active' || isTrialing;
    
    let trialExpired = false;
    let trialExpiringSoon = false;
    let daysUntilExpiration: number | null = null;

    if (subscription.trial_ends_at) {
      const trialEndsAt = new Date(subscription.trial_ends_at);
      const now = new Date();
      const timeDiff = trialEndsAt.getTime() - now.getTime();
      daysUntilExpiration = Math.ceil(timeDiff / (1000 * 60 * 60 * 24));
      
      trialExpired = timeDiff <= 0;
      trialExpiringSoon = timeDiff > 0 && daysUntilExpiration <= 3;
    }

    return {
      hasActiveSubscription,
      isTrialing,
      trialExpired,
      trialExpiringSoon,
      canAccessApp: hasActiveSubscription && (!isTrialing || !trialExpired),
      daysUntilExpiration,
      isLoading: false,
    };
  }, [subscriptions, result.type]);
}

export function useTrialBadge(): TrialBadgeStatus {
  const { isTrialing, trialExpired, trialExpiringSoon, daysUntilExpiration, hasActiveSubscription, isLoading } = useTrialStatus();
  console.log('useTrialBadge', { isTrialing, trialExpired, trialExpiringSoon, daysUntilExpiration, hasActiveSubscription, isLoading });

  return useMemo(() => {
    if (isLoading) {
      return { show: false, variant: 'success', message: '', isLoading: true };
    }

    // If no subscription exists at all (new user), don't show any badge
    // This prevents showing "Trial expired" for users who just signed up
    if (!hasActiveSubscription && !isTrialing) {
      return { show: false, variant: 'success', message: '', isLoading: false };
    }

    // If trial has expired, show expired badge
    if (trialExpired) {
      return { show: true, variant: 'destructive', message: 'Trial expired', isLoading: false };
    }

    // If not trialing (has active subscription), don't show badge
    if (!isTrialing) {
      return { show: false, variant: 'success', message: '', isLoading: false };
    }

    if (trialExpiringSoon && daysUntilExpiration !== null) {
      const days = daysUntilExpiration === 1 ? 'day' : 'days';
      return {
        show: true,
        variant: 'warning',
        message: `Trial expires in ${daysUntilExpiration} ${days}`,
        isLoading: false,
      };
    }

    if (daysUntilExpiration !== null && daysUntilExpiration > 3) {
      const days = daysUntilExpiration === 1 ? 'day' : 'days';
      return {
        show: true,
        variant: 'success',
        message: `Trial expires in ${daysUntilExpiration} ${days}`,
        isLoading: false,
      };
    }

    return { show: false, variant: 'success', message: '', isLoading: false };
  }, [isTrialing, trialExpired, trialExpiringSoon, daysUntilExpiration, hasActiveSubscription, isLoading]);
}
