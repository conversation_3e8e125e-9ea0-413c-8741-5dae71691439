'use client';

import { useEffect } from 'react';
import { useRouter, usePathname } from 'next/navigation';
import { useTrialStatus } from '~/hooks/use-trial-status';
import { useTeamAccountWorkspace } from '@kit/team-accounts/hooks/use-team-account-workspace';

interface TrialGuardProps {
  children: React.ReactNode;
}

/**
 * Trial Guard Component
 * 
 * Protects routes based on subscription/trial status.
 * When trial has expired or no subscription exists:
 * - Allows access only to billing, account, and members pages
 * - Redirects to billing page for all other routes
 */
export function TrialGuard({ children }: TrialGuardProps) {
  const router = useRouter();
  const pathname = usePathname();
  const workspace = useTeamAccountWorkspace();
  const trialStatus = useTrialStatus();
  
  const accountSlug = workspace.account.slug || workspace.account.id;

  // Define allowed paths when trial is expired or no subscription
  const allowedPaths = [
    `/home/<USER>/billing`,
    `/home/<USER>/settings`, // account settings
    `/home/<USER>/members`,
    `/home/<USER>/user-settings`, // user account page
  ];

  // Check if current path is allowed
  const isAllowedPath = allowedPaths.some(allowedPath => 
    pathname.startsWith(allowedPath)
  );

  useEffect(() => {
    // If user can access the app, no need to redirect
    if (trialStatus.canAccessApp) {
      return;
    }

    // If trial expired/no subscription and user is not on an allowed path
    if (!trialStatus.canAccessApp && !isAllowedPath) {
      // Redirect to billing page
      const billingPath = `/home/<USER>/billing`;
      router.push(billingPath);
    }
  }, [
    trialStatus.canAccessApp, 
    isAllowedPath, 
    router, 
    accountSlug, 
    pathname
  ]);

  // Always render children - the redirect happens in useEffect
  // This prevents flash of content and allows the redirect to work properly
  return <>{children}</>;
}

/**
 * Higher-order component version of TrialGuard
 * Can be used to wrap page components
 */
export function withTrialGuard<P extends object>(
  Component: React.ComponentType<P>
): React.ComponentType<P> {
  const WrappedComponent = (props: P) => {
    return (
      <TrialGuard>
        <Component {...props} />
      </TrialGuard>
    );
  };

  WrappedComponent.displayName = `withTrialGuard(${Component.displayName || Component.name})`;
  
  return WrappedComponent;
}
